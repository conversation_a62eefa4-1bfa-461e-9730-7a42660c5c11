
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  name: 'name',
  email: 'email',
  hashedPassword: 'hashedPassword',
  image: 'image',
  phone: 'phone',
  bio: 'bio',
  address: 'address',
  dateOfBirth: 'dateOfBirth',
  isVerified: 'isVerified',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  riskScore: 'riskScore',
  lastLoginIp: 'lastLoginIp',
  lastLoginUserAgent: 'lastLoginUserAgent',
  loginAttempts: 'loginAttempts',
  lastFailedLoginAt: 'lastFailedLoginAt',
  currentLocationId: 'currentLocationId'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  sessionToken: 'sessionToken',
  userId: 'userId',
  expires: 'expires'
};

exports.Prisma.VerificationTokenScalarFieldEnum = {
  identifier: 'identifier',
  token: 'token',
  expires: 'expires'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  price: 'price',
  images: 'images',
  condition: 'condition',
  status: 'status',
  viewCount: 'viewCount',
  featuredUntil: 'featuredUntil',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  sellerId: 'sellerId',
  categoryId: 'categoryId',
  riskScore: 'riskScore',
  isHidden: 'isHidden',
  hiddenReason: 'hiddenReason',
  shareCount: 'shareCount',
  locationId: 'locationId'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  name: 'name',
  slug: 'slug',
  icon: 'icon',
  description: 'description',
  parentId: 'parentId'
};

exports.Prisma.ReviewScalarFieldEnum = {
  id: 'id',
  rating: 'rating',
  comment: 'comment',
  createdAt: 'createdAt',
  productId: 'productId',
  reviewerId: 'reviewerId'
};

exports.Prisma.SellerScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  businessName: 'businessName',
  description: 'description',
  phoneNumber: 'phoneNumber',
  address: 'address',
  rating: 'rating',
  totalSales: 'totalSales',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  phoneVerified: 'phoneVerified',
  idVerified: 'idVerified',
  idVerificationDate: 'idVerificationDate',
  idVerificationMethod: 'idVerificationMethod',
  idVerificationStatus: 'idVerificationStatus'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  status: 'status',
  totalAmount: 'totalAmount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  productId: 'productId',
  buyerId: 'buyerId',
  estimatedDeliveryDate: 'estimatedDeliveryDate',
  trackingNumber: 'trackingNumber',
  notes: 'notes'
};

exports.Prisma.StatusHistoryScalarFieldEnum = {
  id: 'id',
  status: 'status',
  createdAt: 'createdAt',
  updatedBy: 'updatedBy',
  notes: 'notes',
  orderId: 'orderId'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  quantity: 'quantity',
  price: 'price',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  orderId: 'orderId',
  productId: 'productId'
};

exports.Prisma.SettingsScalarFieldEnum = {
  id: 'id',
  siteName: 'siteName',
  siteDescription: 'siteDescription',
  maintenanceMode: 'maintenanceMode',
  allowNewRegistrations: 'allowNewRegistrations',
  requireEmailVerification: 'requireEmailVerification',
  maxProductsPerSeller: 'maxProductsPerSeller',
  commissionRate: 'commissionRate',
  currency: 'currency',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FollowScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  followerId: 'followerId',
  followingId: 'followingId'
};

exports.Prisma.BadgeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  icon: 'icon',
  earnedAt: 'earnedAt',
  userId: 'userId'
};

exports.Prisma.AchievementScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  progress: 'progress',
  target: 'target',
  completed: 'completed',
  reward: 'reward',
  userId: 'userId'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  type: 'type',
  message: 'message',
  read: 'read',
  createdAt: 'createdAt',
  userId: 'userId'
};

exports.Prisma.ChatScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  productId: 'productId',
  buyerId: 'buyerId',
  sellerId: 'sellerId',
  status: 'status'
};

exports.Prisma.MessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  chatId: 'chatId',
  senderId: 'senderId',
  read: 'read'
};

exports.Prisma.MessageAttachmentScalarFieldEnum = {
  id: 'id',
  type: 'type',
  url: 'url',
  name: 'name',
  size: 'size',
  createdAt: 'createdAt',
  messageId: 'messageId'
};

exports.Prisma.SocialAccountScalarFieldEnum = {
  id: 'id',
  provider: 'provider',
  handle: 'handle',
  url: 'url',
  verified: 'verified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  sellerId: 'sellerId'
};

exports.Prisma.DisputeScalarFieldEnum = {
  id: 'id',
  reason: 'reason',
  description: 'description',
  status: 'status',
  resolution: 'resolution',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  orderId: 'orderId',
  openedById: 'openedById',
  assignedToId: 'assignedToId'
};

exports.Prisma.DisputeMessageScalarFieldEnum = {
  id: 'id',
  content: 'content',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  disputeId: 'disputeId',
  senderId: 'senderId',
  isAdminMessage: 'isAdminMessage'
};

exports.Prisma.DisputeEvidenceScalarFieldEnum = {
  id: 'id',
  type: 'type',
  url: 'url',
  description: 'description',
  createdAt: 'createdAt',
  disputeId: 'disputeId',
  uploadedById: 'uploadedById'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceId: 'deviceId',
  location: 'location',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt',
  isRevoked: 'isRevoked'
};

exports.Prisma.FraudReportScalarFieldEnum = {
  id: 'id',
  reason: 'reason',
  description: 'description',
  status: 'status',
  resolution: 'resolution',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  reporterId: 'reporterId',
  productId: 'productId',
  sellerId: 'sellerId',
  reviewedById: 'reviewedById',
  reviewedAt: 'reviewedAt'
};

exports.Prisma.SuspiciousActivityScalarFieldEnum = {
  id: 'id',
  type: 'type',
  description: 'description',
  severity: 'severity',
  createdAt: 'createdAt',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  isResolved: 'isResolved',
  resolvedById: 'resolvedById',
  resolvedAt: 'resolvedAt',
  notes: 'notes'
};

exports.Prisma.SavedSearchScalarFieldEnum = {
  id: 'id',
  name: 'name',
  filters: 'filters',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  isDefault: 'isDefault',
  lastUsed: 'lastUsed'
};

exports.Prisma.ShareEventScalarFieldEnum = {
  id: 'id',
  url: 'url',
  platform: 'platform',
  createdAt: 'createdAt',
  userId: 'userId',
  productId: 'productId',
  userAgent: 'userAgent',
  referrer: 'referrer'
};

exports.Prisma.CategoryAttributeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  options: 'options',
  isRequired: 'isRequired',
  categoryId: 'categoryId'
};

exports.Prisma.LocationScalarFieldEnum = {
  id: 'id',
  address: 'address',
  city: 'city',
  state: 'state',
  country: 'country',
  postalCode: 'postalCode',
  latitude: 'latitude',
  longitude: 'longitude',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserLocationScalarFieldEnum = {
  id: 'id',
  name: 'name',
  address: 'address',
  city: 'city',
  state: 'state',
  country: 'country',
  postalCode: 'postalCode',
  latitude: 'latitude',
  longitude: 'longitude',
  isDefault: 'isDefault',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.VehicleMakeModelScalarFieldEnum = {
  id: 'id',
  make: 'make',
  model: 'model',
  category: 'category',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductViewScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  productId: 'productId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.IdVerificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  documentType: 'documentType',
  frontImageUrl: 'frontImageUrl',
  backImageUrl: 'backImageUrl',
  status: 'status',
  submittedAt: 'submittedAt',
  reviewedAt: 'reviewedAt',
  reviewedBy: 'reviewedBy',
  rejectionReason: 'rejectionReason'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  GUEST: 'GUEST',
  USER: 'USER',
  ADMIN: 'ADMIN'
};

exports.ProductCondition = exports.$Enums.ProductCondition = {
  NEW: 'NEW',
  USED: 'USED',
  REFURBISHED: 'REFURBISHED'
};

exports.ProductStatus = exports.$Enums.ProductStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  SOLD: 'SOLD',
  INACTIVE: 'INACTIVE'
};

exports.VerificationStatus = exports.$Enums.VerificationStatus = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  REJECTED: 'REJECTED'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  INQUIRY: 'INQUIRY',
  AGREEMENT: 'AGREEMENT',
  PAYMENT_PENDING: 'PAYMENT_PENDING',
  PREPARING: 'PREPARING',
  SHIPPING: 'SHIPPING',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
};

exports.ChatStatus = exports.$Enums.ChatStatus = {
  ACTIVE: 'ACTIVE',
  ARCHIVED: 'ARCHIVED',
  BLOCKED: 'BLOCKED'
};

exports.DisputeReason = exports.$Enums.DisputeReason = {
  ITEM_NOT_RECEIVED: 'ITEM_NOT_RECEIVED',
  ITEM_NOT_AS_DESCRIBED: 'ITEM_NOT_AS_DESCRIBED',
  DAMAGED_ITEM: 'DAMAGED_ITEM',
  WRONG_ITEM: 'WRONG_ITEM',
  OTHER: 'OTHER'
};

exports.DisputeStatus = exports.$Enums.DisputeStatus = {
  OPEN: 'OPEN',
  UNDER_REVIEW: 'UNDER_REVIEW',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.FraudReportReason = exports.$Enums.FraudReportReason = {
  COUNTERFEIT: 'COUNTERFEIT',
  MISLEADING: 'MISLEADING',
  PROHIBITED_ITEM: 'PROHIBITED_ITEM',
  SCAM: 'SCAM',
  OTHER: 'OTHER'
};

exports.FraudReportStatus = exports.$Enums.FraudReportStatus = {
  PENDING: 'PENDING',
  UNDER_REVIEW: 'UNDER_REVIEW',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Session: 'Session',
  VerificationToken: 'VerificationToken',
  Product: 'Product',
  Category: 'Category',
  Review: 'Review',
  Seller: 'Seller',
  Order: 'Order',
  StatusHistory: 'StatusHistory',
  OrderItem: 'OrderItem',
  Settings: 'Settings',
  Follow: 'Follow',
  Badge: 'Badge',
  Achievement: 'Achievement',
  Notification: 'Notification',
  Chat: 'Chat',
  Message: 'Message',
  MessageAttachment: 'MessageAttachment',
  SocialAccount: 'SocialAccount',
  Dispute: 'Dispute',
  DisputeMessage: 'DisputeMessage',
  DisputeEvidence: 'DisputeEvidence',
  UserSession: 'UserSession',
  FraudReport: 'FraudReport',
  SuspiciousActivity: 'SuspiciousActivity',
  SavedSearch: 'SavedSearch',
  ShareEvent: 'ShareEvent',
  CategoryAttribute: 'CategoryAttribute',
  Location: 'Location',
  UserLocation: 'UserLocation',
  VehicleMakeModel: 'VehicleMakeModel',
  ProductView: 'ProductView',
  IdVerification: 'IdVerification'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
