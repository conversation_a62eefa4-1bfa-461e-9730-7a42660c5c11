import React from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

interface StartChatButtonProps {
  productId: string;
  sellerId: string;
  className?: string;
}

const StartChatButton: React.FC<StartChatButtonProps> = ({
  productId,
  sellerId,
  className = '',
}) => {
  const router = useRouter();
  const { data: session } = useSession();

  const handleClick = async () => {
    if (!session) {
      router.push('/login');
      return;
    }

    try {
      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          sellerId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create chat');
      }

      const chat = await response.json();
      router.push(`/messages?chatId=${chat.id}`);
    } catch (error) {
      console.error('Error creating chat:', error);
      // You might want to show an error message to the user here
    }
  };

  return (
    <button
      onClick={handleClick}
      className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${className}`}
    >
      Message Seller
    </button>
  );
};

export default StartChatButton;