"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/categories/[categoryId]/attributes/route";
exports.ids = ["app/api/categories/[categoryId]/attributes/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_mac_Documents_AI_Development_marketplace_src_app_api_categories_categoryId_attributes_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/categories/[categoryId]/attributes/route.ts */ \"(rsc)/./src/app/api/categories/[categoryId]/attributes/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/categories/[categoryId]/attributes/route\",\n        pathname: \"/api/categories/[categoryId]/attributes\",\n        filename: \"route\",\n        bundlePath: \"app/api/categories/[categoryId]/attributes/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/api/categories/[categoryId]/attributes/route.ts\",\n    nextConfigOutput,\n    userland: _Users_mac_Documents_AI_Development_marketplace_src_app_api_categories_categoryId_attributes_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/categories/[categoryId]/attributes/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/categories/[categoryId]/attributes/route.ts":
/*!*****************************************************************!*\
  !*** ./src/app/api/categories/[categoryId]/attributes/route.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// GET endpoint to fetch attributes for a specific category\nasync function GET(request, { params }) {\n    try {\n        const { categoryId } = params;\n        // Check if category exists\n        const category = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.category.findUnique({\n            where: {\n                id: categoryId\n            }\n        });\n        if (!category) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                message: \"Category not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Fetch attributes for the category\n        const attributes = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.categoryAttribute.findMany({\n            where: {\n                categoryId\n            },\n            orderBy: {\n                name: \"asc\"\n            }\n        });\n        // If no attributes found in database, return mock attributes based on category slug\n        if (attributes.length === 0) {\n            const mockAttributes = getMockAttributesForCategory(category.slug);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(mockAttributes);\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(attributes);\n    } catch (error) {\n        console.error(\"Error fetching category attributes:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Mock attributes for demo purposes\nfunction getMockAttributesForCategory(categorySlug) {\n    const mockAttributes = {\n        \"electronics\": [\n            {\n                id: \"brand\",\n                name: \"Brand\",\n                type: \"text\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"model\",\n                name: \"Model\",\n                type: \"text\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"storage\",\n                name: \"Storage\",\n                type: \"select\",\n                options: [\n                    \"16GB\",\n                    \"32GB\",\n                    \"64GB\",\n                    \"128GB\",\n                    \"256GB\",\n                    \"512GB\",\n                    \"1TB\",\n                    \"Other\"\n                ],\n                isRequired: false\n            },\n            {\n                id: \"color\",\n                name: \"Color\",\n                type: \"text\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"warranty\",\n                name: \"Warranty Included\",\n                type: \"boolean\",\n                options: [],\n                isRequired: false\n            }\n        ],\n        \"vehicles\": [\n            {\n                id: \"make\",\n                name: \"Make\",\n                type: \"text\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"model\",\n                name: \"Model\",\n                type: \"text\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"year\",\n                name: \"Year\",\n                type: \"number\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"mileage\",\n                name: \"Mileage (km)\",\n                type: \"number\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"fuel_type\",\n                name: \"Fuel Type\",\n                type: \"select\",\n                options: [\n                    \"Gasoline\",\n                    \"Diesel\",\n                    \"Electric\",\n                    \"Hybrid\",\n                    \"Other\"\n                ],\n                isRequired: true\n            },\n            {\n                id: \"transmission\",\n                name: \"Transmission\",\n                type: \"select\",\n                options: [\n                    \"Automatic\",\n                    \"Manual\",\n                    \"Semi-automatic\"\n                ],\n                isRequired: true\n            }\n        ],\n        \"fashion\": [\n            {\n                id: \"size\",\n                name: \"Size\",\n                type: \"select\",\n                options: [\n                    \"XS\",\n                    \"S\",\n                    \"M\",\n                    \"L\",\n                    \"XL\",\n                    \"XXL\",\n                    \"XXXL\"\n                ],\n                isRequired: true\n            },\n            {\n                id: \"color\",\n                name: \"Color\",\n                type: \"text\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"material\",\n                name: \"Material\",\n                type: \"text\",\n                options: [],\n                isRequired: false\n            },\n            {\n                id: \"gender\",\n                name: \"Gender\",\n                type: \"select\",\n                options: [\n                    \"Men\",\n                    \"Women\",\n                    \"Unisex\",\n                    \"Kids\"\n                ],\n                isRequired: true\n            }\n        ],\n        \"real-estate\": [\n            {\n                id: \"property_type\",\n                name: \"Property Type\",\n                type: \"select\",\n                options: [\n                    \"Apartment\",\n                    \"House\",\n                    \"Condo\",\n                    \"Townhouse\",\n                    \"Land\",\n                    \"Commercial\"\n                ],\n                isRequired: true\n            },\n            {\n                id: \"bedrooms\",\n                name: \"Bedrooms\",\n                type: \"number\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"bathrooms\",\n                name: \"Bathrooms\",\n                type: \"number\",\n                options: [],\n                isRequired: true\n            },\n            {\n                id: \"area\",\n                name: \"Area (sq ft)\",\n                type: \"number\",\n                options: [],\n                isRequired: true\n            }\n        ]\n    };\n    return mockAttributes[categorySlug] || [];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/categories/[categoryId]/attributes/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n// For backward compatibility\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFJakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBeUIsRUFBY0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBRXBFLDZCQUE2QjtBQUM3QixpRUFBZUEsTUFBTUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLy4vc3JjL2xpYi9wcmlzbWEudHM/MDFkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkO1xufTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPz8gbmV3IFByaXNtYUNsaWVudCgpO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYTtcblxuLy8gRm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHlcbmV4cG9ydCBkZWZhdWx0IHByaXNtYTsiXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&page=%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2F%5BcategoryId%5D%2Fattributes%2Froute.ts&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();