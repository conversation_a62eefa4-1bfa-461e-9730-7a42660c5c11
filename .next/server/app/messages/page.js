/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/messages/page";
exports.ids = ["app/messages/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'messages',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/messages/page.tsx */ \"(rsc)/./src/app/messages/page.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/messages/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/messages/page\",\n        pathname: \"/messages\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/providers.tsx */ \"(ssr)/./src/app/providers.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZm9udCUyRmdvb2dsZSUyRnRhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTJGYXBwJTJGbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz0lMkZVc2VycyUyRm1hYyUyRkRvY3VtZW50cyUyRkFJJTIwRGV2ZWxvcG1lbnQlMkZtYXJrZXRwbGFjZSUyRnNyYyUyRmFwcCUyRmdsb2JhbHMuY3NzJm1vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZzcmMlMkZhcHAlMkZwcm92aWRlcnMudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLz9lMzdkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hYy9Eb2N1bWVudHMvQUkgRGV2ZWxvcG1lbnQvbWFya2V0cGxhY2Uvc3JjL2FwcC9wcm92aWRlcnMudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fglobals.css&modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fproviders.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fmessages%2Fpage.tsx&server=true!":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fmessages%2Fpage.tsx&server=true! ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/messages/page.tsx */ \"(ssr)/./src/app/messages/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZtYWMlMkZEb2N1bWVudHMlMkZBSSUyMERldmVsb3BtZW50JTJGbWFya2V0cGxhY2UlMkZzcmMlMkZhcHAlMkZtZXNzYWdlcyUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL21hcmtldHBsYWNlLz80ZWJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hYy9Eb2N1bWVudHMvQUkgRGV2ZWxvcG1lbnQvbWFya2V0cGxhY2Uvc3JjL2FwcC9tZXNzYWdlcy9wYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp%2Fmessages%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/messages/page.tsx":
/*!***********************************!*\
  !*** ./src/app/messages/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessagesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_ChatWindow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ChatWindow */ \"(ssr)/./src/components/ChatWindow.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./src/lib/data.ts\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useSocket */ \"(ssr)/./src/hooks/useSocket.ts\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MagnifyingGlassIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction MessagesPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [chats, setChats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [chatSearchQuery, setChatSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { isConnected, on, off } = (0,_hooks_useSocket__WEBPACK_IMPORTED_MODULE_9__.useSocket)();\n    // Redirect if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (!session) {\n            router.push(\"/auth/signin?callbackUrl=/messages\");\n        }\n    }, [\n        session,\n        status,\n        router\n    ]);\n    // Fetch chats and handle chatId from URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session?.user) {\n            fetchChats();\n            // Check if there's a chatId in the URL\n            const urlParams = new URLSearchParams(window.location.search);\n            const chatId = urlParams.get(\"chatId\");\n            if (chatId) {\n                // Find and select the chat after chats are loaded\n                setTimeout(()=>{\n                    const chat = chats.find((c)=>c.id === chatId);\n                    if (chat) {\n                        setSelectedChat(chat);\n                    }\n                }, 1000);\n            }\n        }\n    }, [\n        session,\n        chats\n    ]);\n    // Listen for real-time updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleNewMessage = (message)=>{\n            setChats((prevChats)=>prevChats.map((chat)=>chat.id === message.chatId ? {\n                        ...chat,\n                        messages: [\n                            message\n                        ],\n                        updatedAt: message.createdAt,\n                        _count: {\n                            ...chat._count,\n                            messages: message.senderId !== session?.user?.id ? chat._count.messages + 1 : chat._count.messages\n                        }\n                    } : chat));\n        };\n        if (isConnected) {\n            on(\"new-message\", handleNewMessage);\n        }\n        return ()=>{\n            if (isConnected) {\n                off(\"new-message\", handleNewMessage);\n            }\n        };\n    }, [\n        isConnected,\n        session?.user?.id,\n        on,\n        off\n    ]);\n    const fetchChats = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(\"/api/chats\");\n            if (response.ok) {\n                const data = await response.json();\n                setChats(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching chats:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const filteredChats = chats.filter((chat)=>{\n        const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;\n        return otherUser.name.toLowerCase().includes(chatSearchQuery.toLowerCase()) || chat.product.name.toLowerCase().includes(chatSearchQuery.toLowerCase());\n    });\n    const formatLastMessageTime = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 1) {\n            return \"Just now\";\n        } else if (diffInHours < 24) {\n            return `${Math.floor(diffInHours)}h ago`;\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null; // Will redirect\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                searchQuery: searchQuery,\n                setSearchQuery: setSearchQuery,\n                isMenuOpen: isMenuOpen,\n                setIsMenuOpen: setIsMenuOpen\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-8\",\n                            children: \"Messages\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                            style: {\n                                height: \"600px\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1/3 border-r border-gray-200 flex flex-col\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search conversations...\",\n                                                            value: chatSearchQuery,\n                                                            onChange: (e)=>setChatSearchQuery(e.target.value),\n                                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center py-8\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this) : filteredChats.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"No conversations yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>router.push(\"/\"),\n                                                            className: \"mt-4 text-indigo-600 hover:text-indigo-700 font-medium\",\n                                                            children: \"Browse Products\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this) : filteredChats.map((chat)=>{\n                                                    const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;\n                                                    const lastMessage = chat.messages[0];\n                                                    const unreadCount = chat._count.messages;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        onClick: ()=>setSelectedChat(chat),\n                                                        className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedChat?.id === chat.id ? \"bg-indigo-50 border-indigo-200\" : \"\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        otherUser.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            src: otherUser.image,\n                                                                            alt: otherUser.name,\n                                                                            width: 48,\n                                                                            height: 48,\n                                                                            className: \"rounded-full object-cover\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 33\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 font-semibold\",\n                                                                            children: otherUser.name.charAt(0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                                            children: unreadCount > 9 ? \"9+\" : unreadCount\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                    lineNumber: 240,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-semibold text-gray-900 truncate\",\n                                                                                    children: otherUser.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                                    lineNumber: 263,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-gray-500\",\n                                                                                    children: formatLastMessageTime(lastMessage.createdAt)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                                    lineNumber: 265,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 truncate\",\n                                                                            children: chat.product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        lastMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-500 truncate mt-1\",\n                                                                            children: [\n                                                                                lastMessage.sender.id === session?.user?.id ? \"You: \" : \"\",\n                                                                                lastMessage.content\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                            lineNumber: 272,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, chat.id, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            chat: selectedChat,\n                                            onClose: ()=>setSelectedChat(null)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-full flex items-center justify-center text-gray-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MagnifyingGlassIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg\",\n                                                        children: \"Select a conversation to start messaging\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                categories: _lib_data__WEBPACK_IMPORTED_MODULE_8__.categories\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/messages/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/UserContext */ \"(ssr)/./src/context/UserContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_3__.UserProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ3dCO0FBQ0k7QUFFdkMsU0FBU0csVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQzNFLHFCQUNFLDhEQUFDSCw0REFBZUE7a0JBQ2QsNEVBQUNDLDhEQUFZQTtzQkFDVkU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvcHJvdmlkZXJzLnRzeD85MzI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0L1VzZXJDb250ZXh0JztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XG4gICk7XG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsIlNlc3Npb25Qcm92aWRlciIsIlVzZXJQcm92aWRlciIsIlByb3ZpZGVycyIsImNoaWxkcmVuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatWindow.tsx":
/*!***************************************!*\
  !*** ./src/components/ChatWindow.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"(ssr)/./src/hooks/useSocket.ts\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PaperAirplaneIcon,PaperClipIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PaperAirplaneIcon,PaperClipIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaperClipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PaperAirplaneIcon,PaperClipIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=EllipsisVerticalIcon,PaperAirplaneIcon,PaperClipIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ChatWindow({ chat, onClose }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSending, setIsSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [typingUsers, setTypingUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const typingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const { isConnected, joinChat, leaveChat, sendMessage, startTyping, stopTyping, markMessagesAsRead, on, off } = (0,_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__.useSocket)();\n    const otherUser = chat.buyer.id === session?.user?.id ? chat.seller : chat.buyer;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isConnected) {\n            joinChat(chat.id);\n            fetchMessages();\n        }\n        return ()=>{\n            if (isConnected) {\n                leaveChat(chat.id);\n            }\n        };\n    }, [\n        chat.id,\n        isConnected\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleNewMessage = (message)=>{\n            if (message.chatId === chat.id) {\n                setMessages((prev)=>[\n                        ...prev,\n                        message\n                    ]);\n                scrollToBottom();\n                // Mark as read if not from current user\n                if (message.senderId !== session?.user?.id) {\n                    markMessagesAsRead(chat.id);\n                }\n            }\n        };\n        const handleUserTyping = (data)=>{\n            if (data.userId !== session?.user?.id) {\n                setTypingUsers((prev)=>[\n                        ...prev.filter((id)=>id !== data.userId),\n                        data.userId\n                    ]);\n            }\n        };\n        const handleUserStoppedTyping = (data)=>{\n            setTypingUsers((prev)=>prev.filter((id)=>id !== data.userId));\n        };\n        on(\"new-message\", handleNewMessage);\n        on(\"user-typing\", handleUserTyping);\n        on(\"user-stopped-typing\", handleUserStoppedTyping);\n        return ()=>{\n            off(\"new-message\", handleNewMessage);\n            off(\"user-typing\", handleUserTyping);\n            off(\"user-stopped-typing\", handleUserStoppedTyping);\n        };\n    }, [\n        chat.id,\n        session?.user?.id,\n        on,\n        off,\n        markMessagesAsRead\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const fetchMessages = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch(`/api/chats/${chat.id}`);\n            if (response.ok) {\n                const data = await response.json();\n                setMessages(data.messages);\n            }\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const handleSendMessage = async (e)=>{\n        e.preventDefault();\n        if (!newMessage.trim() || isSending) return;\n        const messageContent = newMessage.trim();\n        setNewMessage(\"\");\n        setIsSending(true);\n        try {\n            if (isConnected) {\n                sendMessage(chat.id, messageContent);\n            } else {\n                // Fallback to HTTP API if socket not connected\n                const response = await fetch(`/api/chats/${chat.id}`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        content: messageContent\n                    })\n                });\n                if (response.ok) {\n                    const message = await response.json();\n                    setMessages((prev)=>[\n                            ...prev,\n                            message\n                        ]);\n                }\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setNewMessage(messageContent); // Restore message on error\n        } finally{\n            setIsSending(false);\n        }\n    };\n    const handleTyping = ()=>{\n        if (isConnected) {\n            startTyping(chat.id);\n            // Clear existing timeout\n            if (typingTimeoutRef.current) {\n                clearTimeout(typingTimeoutRef.current);\n            }\n            // Stop typing after 3 seconds of inactivity\n            typingTimeoutRef.current = setTimeout(()=>{\n                stopTyping(chat.id);\n            }, 3000);\n        }\n    };\n    const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        if (date.toDateString() === today.toDateString()) {\n            return \"Today\";\n        } else if (date.toDateString() === yesterday.toDateString()) {\n            return \"Yesterday\";\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-10 h-10\",\n                                children: [\n                                    otherUser.image ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: otherUser.image,\n                                        alt: otherUser.name,\n                                        fill: true,\n                                        className: \"rounded-full object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 font-semibold\",\n                                        children: otherUser.name.charAt(0)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: otherUser.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: chat.product.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-lg font-bold text-indigo-600\",\n                                children: [\n                                    \"₦\",\n                                    chat.product.price.toLocaleString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-gray-50 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        chat.product.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-12 h-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: chat.product.images[0],\n                                alt: chat.product.name,\n                                fill: true,\n                                className: \"rounded-lg object-cover\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: chat.product.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: [\n                                        \"₦\",\n                                        chat.product.price.toLocaleString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `px-2 py-1 text-xs rounded-full ${chat.product.status === \"ACTIVE\" ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}`,\n                            children: chat.product.status\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this) : messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-gray-500\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No messages yet. Start the conversation!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, this) : messages.map((message, index)=>{\n                        const isOwnMessage = message.senderId === session?.user?.id;\n                        const showDate = index === 0 || formatDate(messages[index - 1].createdAt) !== formatDate(message.createdAt);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full\",\n                                        children: formatDate(message.createdAt)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${isOwnMessage ? \"justify-end\" : \"justify-start\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isOwnMessage ? \"bg-indigo-600 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: message.content\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 21\n                                            }, this),\n                                            message.attachments && message.attachments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 space-y-1\",\n                                                children: message.attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: attachment.url,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"underline\",\n                                                            children: attachment.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, attachment.id, false, {\n                                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: `text-xs mt-1 ${isOwnMessage ? \"text-indigo-200\" : \"text-gray-500\"}`,\n                                                children: formatTime(message.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 15\n                        }, this);\n                    }),\n                    typingUsers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-start\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-100 text-gray-900 px-4 py-2 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.1s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                style: {\n                                                    animationDelay: \"0.2s\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-gray-500 ml-2\",\n                                        children: \"typing...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSendMessage,\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: newMessage,\n                                onChange: (e)=>{\n                                    setNewMessage(e.target.value);\n                                    handleTyping();\n                                },\n                                placeholder: \"Type a message...\",\n                                className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\",\n                                disabled: isSending\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !newMessage.trim() || isSending,\n                                className: \"p-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EllipsisVerticalIcon_PaperAirplaneIcon_PaperClipIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-yellow-600 mt-2\",\n                        children: \"Real-time messaging unavailable. Messages will be sent when connection is restored.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/ChatWindow.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatWindow.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer({ categories }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-white border-t border-slate-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: `/categories/${category.slug}`,\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, category.id, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/products\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"All Products\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/sellers\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Top Sellers\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/contact\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Help & Support\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/faqs\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"FAQ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/shipping\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Shipping Info\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/returns\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Returns\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/privacy-policy\",\n                                                className: \"text-slate-600 hover:text-slate-900\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Newsletter\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 mb-4\",\n                                    children: \"Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your email\",\n                                            className: \"flex-1 px-4 py-2 rounded-lg border border-slate-200 focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition\",\n                                            children: \"Subscribe\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 pt-8 border-t border-slate-100 text-center text-slate-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"\\xa9 \",\n                            new Date().getFullYear(),\n                            \" Marketplace. All rights reserved.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _Icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Icons */ \"(ssr)/./src/components/Icons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header({ searchQuery, setSearchQuery, isMenuOpen, setIsMenuOpen, cart = [] }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            href: \"/\",\n                            className: \"text-2xl font-bold text-indigo-600\",\n                            children: \"Marketplace\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex flex-1 max-w-2xl mx-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                        placeholder: \"Search for properties, vehicles, or gadgets...\",\n                                        className: \"w-full px-4 py-2 pl-10 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/products\",\n                                    className: \"text-slate-600 hover:text-slate-900\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/sellers\",\n                                    className: \"text-slate-600 hover:text-slate-900\",\n                                    children: \"Sellers\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/sell\",\n                                    className: \"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors font-medium\",\n                                    children: \"Sell\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"md:hidden p-2 text-slate-600 hover:text-slate-900\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.CloseIcon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.MenuIcon, {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                placeholder: \"Search for properties, vehicles, or gadgets...\",\n                                className: \"w-full px-4 py-2 pl-10 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Icons__WEBPACK_IMPORTED_MODULE_2__.SearchIcon, {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden py-4 border-t border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/products\",\n                                className: \"text-slate-600 hover:text-slate-900\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Products\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/sellers\",\n                                className: \"text-slate-600 hover:text-slate-900\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Sellers\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                href: \"/sell\",\n                                className: \"bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors font-medium text-center\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Sell\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Icons.tsx":
/*!**********************************!*\
  !*** ./src/components/Icons.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BookmarkIcon: () => (/* binding */ BookmarkIcon),\n/* harmony export */   ChatIcon: () => (/* binding */ ChatIcon),\n/* harmony export */   CheckIcon: () => (/* binding */ CheckIcon),\n/* harmony export */   ChevronDownIcon: () => (/* binding */ ChevronDownIcon),\n/* harmony export */   ChevronLeftIcon: () => (/* binding */ ChevronLeftIcon),\n/* harmony export */   ChevronRightIcon: () => (/* binding */ ChevronRightIcon),\n/* harmony export */   ClockIcon: () => (/* binding */ ClockIcon),\n/* harmony export */   CloseIcon: () => (/* binding */ CloseIcon),\n/* harmony export */   FacebookIcon: () => (/* binding */ FacebookIcon),\n/* harmony export */   FilterIcon: () => (/* binding */ FilterIcon),\n/* harmony export */   FlagIcon: () => (/* binding */ FlagIcon),\n/* harmony export */   HeartIcon: () => (/* binding */ HeartIcon),\n/* harmony export */   IdentificationIcon: () => (/* binding */ IdentificationIcon),\n/* harmony export */   InstagramIcon: () => (/* binding */ InstagramIcon),\n/* harmony export */   LocationIcon: () => (/* binding */ LocationIcon),\n/* harmony export */   MenuIcon: () => (/* binding */ MenuIcon),\n/* harmony export */   PhoneIcon: () => (/* binding */ PhoneIcon),\n/* harmony export */   SearchIcon: () => (/* binding */ SearchIcon),\n/* harmony export */   ShieldCheckIcon: () => (/* binding */ ShieldCheckIcon),\n/* harmony export */   ShoppingCartIcon: () => (/* binding */ ShoppingCartIcon),\n/* harmony export */   SortIcon: () => (/* binding */ SortIcon),\n/* harmony export */   StarIcon: () => (/* binding */ StarIcon),\n/* harmony export */   TagIcon: () => (/* binding */ TagIcon),\n/* harmony export */   TwitterIcon: () => (/* binding */ TwitterIcon),\n/* harmony export */   UserIcon: () => (/* binding */ UserIcon)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst SearchIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 7,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined);\nconst ShoppingCartIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 13,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined);\nconst HeartIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 19,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined);\nconst UserIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 25,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\nconst MenuIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M4 6h16M4 12h16M4 18h16\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 31,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 30,\n        columnNumber: 3\n    }, undefined);\nconst CloseIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M6 18L18 6M6 6l12 12\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 37,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\nfunction StarIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\nfunction CheckIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M5 13l4 4L19 7\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\nfunction ChatIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction LocationIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nconst FacebookIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M20 10c0-5.523-4.477-10-10-10S0 4.477 0 10c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V10h2.54V7.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V10h2.773l-.443 2.89h-2.33v6.988C16.343 19.128 20 14.991 20 10z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 125,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 124,\n        columnNumber: 3\n    }, undefined);\nconst TwitterIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 131,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 130,\n        columnNumber: 3\n    }, undefined);\nconst InstagramIcon = ({ className = \"h-5 w-5\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        viewBox: \"0 0 20 20\",\n        fill: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\",\n            clipRule: \"evenodd\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 137,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 136,\n        columnNumber: 3\n    }, undefined);\nconst FilterIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: className,\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 143,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 142,\n        columnNumber: 3\n    }, undefined);\nconst SortIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M3 7.5L7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 156,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 148,\n        columnNumber: 3\n    }, undefined);\nconst ChevronLeftIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15.75 19.5L8.25 12l7.5-7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 173,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 165,\n        columnNumber: 3\n    }, undefined);\nconst ChevronRightIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M8.25 4.5l7.5 7.5-7.5 7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 190,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 182,\n        columnNumber: 3\n    }, undefined);\nconst FlagIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M3 3v1.5M3 21v-6m0 0l2.77-.693a9 9 0 016.208.682l.108.054a9 9 0 006.086.71l3.114-.732a48.524 48.524 0 01-.005-10.499l-3.11.732a9 9 0 01-6.085-.711l-.108-.054a9 9 0 00-6.208-.682L3 4.5M3 15V4.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 207,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 199,\n        columnNumber: 3\n    }, undefined);\nconst ShieldCheckIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 224,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 216,\n        columnNumber: 3\n    }, undefined);\nconst PhoneIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 241,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 233,\n        columnNumber: 3\n    }, undefined);\nconst IdentificationIcon = ({ className = \"w-6 h-6\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5zm6-10.125a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0zm1.294 6.336a6.721 6.721 0 01-3.17.789 6.721 6.721 0 01-3.168-.789 3.376 3.376 0 016.338 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 258,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 250,\n        columnNumber: 3\n    }, undefined);\nfunction ChevronDownIcon({ className = \"w-6 h-6\" }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M19.5 8.25l-7.5 7.5-7.5-7.5\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 268,\n        columnNumber: 5\n    }, this);\n}\nfunction ClockIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 295,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, this);\n}\nfunction BookmarkIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0111.186 0z\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n            lineNumber: 314,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, this);\n}\nfunction TagIcon({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M6 6h.008v.008H6V6z\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/components/Icons.tsx\",\n        lineNumber: 325,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Icons.tsx\n");

/***/ }),

/***/ "(ssr)/./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ UserProvider,useUser auto */ \n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction UserProvider({ children }) {\n    const [wishlist, setWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentlyViewed, setRecentlyViewed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load wishlist from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedWishlist = localStorage.getItem(\"wishlist\");\n        if (savedWishlist) {\n            setWishlist(JSON.parse(savedWishlist));\n        }\n    }, []);\n    // Save wishlist to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        localStorage.setItem(\"wishlist\", JSON.stringify(wishlist));\n    }, [\n        wishlist\n    ]);\n    const addToWishlist = (product)=>{\n        setWishlist((prev)=>{\n            if (!prev.find((p)=>p.id === product.id)) {\n                return [\n                    ...prev,\n                    product\n                ];\n            }\n            return prev;\n        });\n    };\n    const removeFromWishlist = (productId)=>{\n        setWishlist((prev)=>prev.filter((p)=>p.id !== productId));\n    };\n    const addToRecentlyViewed = (product)=>{\n        setRecentlyViewed((prev)=>{\n            const filtered = prev.filter((p)=>p.id !== product.id);\n            return [\n                product,\n                ...filtered\n            ].slice(0, 10); // Keep only last 10 items\n        });\n    };\n    const clearRecentlyViewed = ()=>{\n        setRecentlyViewed([]);\n    };\n    const isInWishlist = (productId)=>{\n        return wishlist.some((p)=>p.id === productId);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            wishlist,\n            recentlyViewed,\n            addToWishlist,\n            removeFromWishlist,\n            addToRecentlyViewed,\n            clearRecentlyViewed,\n            isInWishlist\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/context/UserContext.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\nfunction useUser() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/context/UserContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSocket.ts":
/*!********************************!*\
  !*** ./src/hooks/useSocket.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSocket,default auto */ \n\n\nfunction useSocket() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [connectionError, setConnectionError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (status === \"loading\") return;\n        if (session?.user && !socketRef.current) {\n            console.log(\"Initializing socket connection...\");\n            const newSocket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)( false ? 0 : \"http://localhost:3000\", {\n                path: \"/api/socket\",\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                timeout: 20000,\n                forceNew: true\n            });\n            newSocket.on(\"connect\", ()=>{\n                console.log(\"Socket connected:\", newSocket.id);\n                setIsConnected(true);\n                setConnectionError(null);\n            });\n            newSocket.on(\"disconnect\", (reason)=>{\n                console.log(\"Socket disconnected:\", reason);\n                setIsConnected(false);\n            });\n            newSocket.on(\"connect_error\", (error)=>{\n                console.error(\"Socket connection error:\", error);\n                setConnectionError(error.message);\n                setIsConnected(false);\n            });\n            socketRef.current = newSocket;\n            setSocket(newSocket);\n        }\n        return ()=>{\n            if (socketRef.current) {\n                console.log(\"Cleaning up socket connection...\");\n                socketRef.current.disconnect();\n                socketRef.current = null;\n                setSocket(null);\n                setIsConnected(false);\n            }\n        };\n    }, [\n        session,\n        status\n    ]);\n    const joinChat = (chatId)=>{\n        if (socket && isConnected) {\n            socket.emit(\"join-chat\", chatId);\n        }\n    };\n    const leaveChat = (chatId)=>{\n        if (socket && isConnected) {\n            socket.emit(\"leave-chat\", chatId);\n        }\n    };\n    const sendMessage = (chatId, content, attachments)=>{\n        if (socket && isConnected) {\n            socket.emit(\"send-message\", {\n                chatId,\n                content,\n                attachments\n            });\n        }\n    };\n    const startTyping = (chatId)=>{\n        if (socket && isConnected) {\n            socket.emit(\"typing-start\", chatId);\n        }\n    };\n    const stopTyping = (chatId)=>{\n        if (socket && isConnected) {\n            socket.emit(\"typing-stop\", chatId);\n        }\n    };\n    const markMessagesAsRead = (chatId)=>{\n        if (socket && isConnected) {\n            socket.emit(\"mark-messages-read\", chatId);\n        }\n    };\n    const on = (event, callback)=>{\n        if (socket) {\n            socket.on(event, callback);\n        }\n    };\n    const off = (event, callback)=>{\n        if (socket) {\n            socket.off(event, callback);\n        }\n    };\n    return {\n        socket,\n        isConnected,\n        connectionError,\n        joinChat,\n        leaveChat,\n        sendMessage,\n        startTyping,\n        stopTyping,\n        markMessagesAsRead,\n        on,\n        off\n    };\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useSocket);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSocket.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categories: () => (/* binding */ categories),\n/* harmony export */   featuredSellers: () => (/* binding */ featuredSellers),\n/* harmony export */   products: () => (/* binding */ products)\n/* harmony export */ });\nconst categories = [\n    {\n        id: 1,\n        name: \"Real Estate\",\n        description: \"Properties for sale and rent including houses, apartments, and commercial spaces\",\n        slug: \"real-estate\",\n        icon: \"\\uD83C\\uDFE0\",\n        count: 150\n    },\n    {\n        id: 2,\n        name: \"Vehicles\",\n        description: \"Cars, motorcycles, and other vehicles for sale\",\n        slug: \"vehicles\",\n        icon: \"\\uD83D\\uDE97\",\n        count: 200\n    },\n    {\n        id: 3,\n        name: \"Gadgets\",\n        description: \"Electronics, smartphones, and other tech gadgets\",\n        slug: \"gadgets\",\n        icon: \"\\uD83D\\uDCF1\",\n        count: 300\n    }\n];\nconst products = [\n    {\n        id: 1,\n        name: \"Modern 3-Bedroom Apartment\",\n        price: \"$250,000\",\n        image: \"/images/apartment.jpg\",\n        category: \"real-estate\",\n        description: \"Spacious 3-bedroom apartment in prime location with modern amenities.\",\n        condition: \"New\",\n        location: \"Lagos, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 1,\n            name: \"PrimeProperties\",\n            rating: 4.8\n        }\n    },\n    {\n        id: 2,\n        name: \"2022 Toyota Camry\",\n        price: \"$35,000\",\n        image: \"/images/camry.jpg\",\n        category: \"vehicles\",\n        description: \"Well-maintained Toyota Camry with low mileage and full service history.\",\n        condition: \"Used\",\n        location: \"Abuja, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 2,\n            name: \"AutoMasters\",\n            rating: 4.9\n        }\n    },\n    {\n        id: 3,\n        name: \"iPhone 15 Pro Max\",\n        price: \"$1,199\",\n        image: \"/images/iphone.jpg\",\n        category: \"gadgets\",\n        description: \"Latest iPhone model with Pro camera system and A17 Pro chip.\",\n        condition: \"New\",\n        location: \"Lagos, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 3,\n            name: \"TechStore\",\n            rating: 4.7\n        }\n    },\n    {\n        id: 4,\n        name: \"Luxury Villa\",\n        price: \"$500,000\",\n        image: \"/images/villa.jpg\",\n        category: \"real-estate\",\n        description: \"Stunning 5-bedroom villa with pool and garden in exclusive neighborhood.\",\n        condition: \"New\",\n        location: \"Port Harcourt, Nigeria\",\n        availability: \"Available\",\n        seller: {\n            id: 1,\n            name: \"PrimeProperties\",\n            rating: 4.8\n        }\n    }\n];\nconst featuredSellers = [\n    {\n        id: 1,\n        name: \"TechStore\",\n        rating: 4.8,\n        activeListings: 45,\n        joined: \"2022\",\n        location: \"Lagos, Nigeria\",\n        responseTime: \"< 1 hour\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 2,\n            title: \"Silver\",\n            points: 1500,\n            nextLevelPoints: 2000\n        },\n        performance: {\n            totalSales: 15000,\n            averageRating: 4.8,\n            responseRate: 98,\n            completionRate: 99,\n            disputeRate: 1\n        },\n        badges: [],\n        achievements: []\n    },\n    {\n        id: 2,\n        name: \"MobileWorld\",\n        rating: 4.5,\n        activeListings: 32,\n        joined: \"2021\",\n        location: \"Abuja, Nigeria\",\n        responseTime: \"< 2 hours\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 1,\n            title: \"Bronze\",\n            points: 800,\n            nextLevelPoints: 1000\n        },\n        performance: {\n            totalSales: 8000,\n            averageRating: 4.5,\n            responseRate: 95,\n            completionRate: 97,\n            disputeRate: 2\n        },\n        badges: [],\n        achievements: []\n    },\n    {\n        id: 3,\n        name: \"ShoeStore\",\n        rating: 4.9,\n        activeListings: 28,\n        joined: \"2023\",\n        location: \"Port Harcourt, Nigeria\",\n        responseTime: \"< 1 hour\",\n        verificationStatus: \"Verified\",\n        email: \"<EMAIL>\",\n        phone: \"+234 ************\",\n        level: {\n            current: 3,\n            title: \"Gold\",\n            points: 2500,\n            nextLevelPoints: 3000\n        },\n        performance: {\n            totalSales: 25000,\n            averageRating: 4.9,\n            responseRate: 99,\n            completionRate: 100,\n            disputeRate: 0\n        },\n        badges: [],\n        achievements: []\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"149448ddc89a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbWFya2V0cGxhY2UvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzJlNDAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxNDk0NDhkZGM4OWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./src/app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Marketplace\",\n    description: \"A modern marketplace for buying and selling products\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQ2E7QUFJNUIsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1YsK0pBQWU7c0JBQzlCLDRFQUFDQyxrREFBU0E7MEJBQ1BLOzs7Ozs7Ozs7Ozs7Ozs7O0FBS1giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tYXJrZXRwbGFjZS8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IFByb3ZpZGVycyBmcm9tICcuL3Byb3ZpZGVycydcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFsnbGF0aW4nXSB9KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ01hcmtldHBsYWNlJyxcbiAgZGVzY3JpcHRpb246ICdBIG1vZGVybiBtYXJrZXRwbGFjZSBmb3IgYnV5aW5nIGFuZCBzZWxsaW5nIHByb2R1Y3RzJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L1Byb3ZpZGVycz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0gIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/messages/page.tsx":
/*!***********************************!*\
  !*** ./src/app/messages/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/app/messages/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/providers.tsx":
/*!*******************************!*\
  !*** ./src/app/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@heroicons","vendor-chunks/engine.io-client","vendor-chunks/ws","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmessages%2Fpage&page=%2Fmessages%2Fpage&appPaths=%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmac%2FDocuments%2FAI%20Development%2Fmarketplace&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();