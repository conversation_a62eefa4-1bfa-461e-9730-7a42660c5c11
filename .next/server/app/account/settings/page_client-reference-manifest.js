globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/account/settings/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/providers.tsx":{"*":{"id":"(ssr)/./src/app/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/FAQ.tsx":{"*":{"id":"(ssr)/./src/components/FAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Newsletter.tsx":{"*":{"id":"(ssr)/./src/components/Newsletter.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PersonalizedRecommendations.tsx":{"*":{"id":"(ssr)/./src/components/PersonalizedRecommendations.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/account/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/account/settings/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/app/providers.tsx":{"id":"(app-pages-browser)/./src/app/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/components/Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/components/Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/components/FAQ.tsx":{"id":"(app-pages-browser)/./src/components/FAQ.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/components/Newsletter.tsx":{"id":"(app-pages-browser)/./src/components/Newsletter.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/components/PersonalizedRecommendations.tsx":{"id":"(app-pages-browser)/./src/components/PersonalizedRecommendations.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/AI Development/marketplace/src/app/account/settings/page.tsx":{"id":"(app-pages-browser)/./src/app/account/settings/page.tsx","name":"*","chunks":["app/account/settings/page","static/chunks/app/account/settings/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/AI Development/marketplace/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Documents/AI Development/marketplace/src/app/page":[],"/Users/<USER>/Documents/AI Development/marketplace/src/app/account/settings/page":[]}}